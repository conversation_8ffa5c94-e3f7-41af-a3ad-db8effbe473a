import time
import threading
from datetime import datetime, timedelta, timezone

import pytest
import requests
from werkzeug.serving import make_server

from promptyoself.app import create_app
from promptyoself.app.database import db
from promptyoself.app.jobs.reminder_jobs import check_due_reminders


class ServerThread(threading.Thread):
    def __init__(self, app, host="127.0.0.1", port=5010):
        super().__init__()
        self.app = app
        self.host = host
        self.port = port
        self.server = None

    def run(self):
        self.server = make_server(self.host, self.port, self.app)
        self.server.serve_forever()

    def shutdown(self):
        if self.server:
            self.server.shutdown()


@pytest.fixture(scope="session")
def live_server():
    app = create_app("tests.settings")
    with app.app_context():
        db.create_all()
    server = ServerThread(app)
    server.start()
    time.sleep(1)
    yield {"url": f"http://{server.host}:{server.port}", "app": app}
    server.shutdown()
    server.join()


def create_project_task(base_url):
    resp = requests.post(
        f"{base_url}/api/projects/",
        json={"name": "E2E Project", "description": "demo"},
        timeout=5,
    )
    resp.raise_for_status()
    project_id = resp.json()["project"]["id"]
    resp = requests.post(
        f"{base_url}/api/tasks/",
        json={
            "name": "E2E Task",
            "description": "demo",
            "project_id": project_id,
            "parent_task_id": None,
        },
        timeout=5,
    )
    resp.raise_for_status()
    task_id = resp.json()["task"]["id"]
    return task_id


@pytest.mark.playwright
def test_user_registration_flow(page, live_server):
    base_url = live_server["url"]
    username = f"user{int(time.time())}"
    page.goto(f"{base_url}/register/")
    page.fill("input[name=username]", username)
    page.fill("input[name=email]", f"{username}@example.com")
    page.fill("input[name=password]", "secret123")
    page.fill("input[name=confirm]", "secret123")
    page.click("text=Register")
    page.wait_for_url(f"{base_url}/")
    assert page.locator("text=User created successfully").is_visible()


@pytest.mark.playwright
def test_reminder_creation_and_delivery(page, live_server):
    base_url = live_server["url"]
    app = live_server["app"]
    task_id = create_project_task(base_url)

    page.goto(f"{base_url}/reminders/new")
    page.select_option("select[name=task_id]", str(task_id))
    page.fill("textarea[name=message]", "E2E reminder")
    # Use longer delay to reduce flakiness
    run_time = (datetime.now(timezone.utc) + timedelta(seconds=5)
                ).strftime("%Y-%m-%dT%H:%M")
    page.fill("input[name=next_run]", run_time)
    page.select_option("select[name=recurrence]", "")
    page.fill("input[name=process_name]", "test_agent")
    page.click("text=Save Reminder")
    page.wait_for_url(f"{base_url}/reminders/")
    assert page.locator("text=E2E reminder").first.is_visible()

    resp = requests.get(f"{base_url}/api/self-prompts/", timeout=5)
    resp.raise_for_status()
    reminders = resp.json()["reminders"]
    reminder = [r for r in reminders if r["message"] == "E2E reminder"][0]

    # Poll for reminder status instead of fixed sleep
    max_wait = 10
    start_time = time.time()
    while time.time() - start_time < max_wait:
        with app.app_context():
            check_due_reminders(app)

        resp = requests.get(
            f"{base_url}/api/self-prompts/{reminder['id']}", timeout=5)
        resp.raise_for_status()
        if resp.json()["reminder"]["status"] in ["sent", "failed_delivery"]:
            break
        time.sleep(1)

    # Final assertion
    resp = requests.get(
        f"{base_url}/api/self-prompts/{reminder['id']}", timeout=5)
    resp.raise_for_status()
    # The reminder should be processed (either sent or failed_delivery since test_agent doesn't exist)
    assert resp.json()["reminder"]["status"] in ["sent", "failed_delivery"]


@pytest.mark.playwright
def test_task_creation_without_parent(page, live_server):
    """Test creating a task without a parent task."""
    base_url = live_server["url"]

    # First create a project via API
    project_data = {"name": "E2E Test Project", "description": "Test project for E2E"}
    resp = requests.post(f"{base_url}/api/projects/", json=project_data, timeout=5)
    resp.raise_for_status()
    project_id = resp.json()["project"]["id"]

    # Navigate to task creation page
    page.goto(f"{base_url}/tasks/new")

    # Fill out the task form without selecting a parent task
    page.fill("input[name=name]", "E2E Test Task")
    page.fill("textarea[name=description]", "Test task without parent")
    page.select_option("select[name=project_id]", str(project_id))
    # Leave parent_task_id as "No Parent Task" (default)
    page.select_option("select[name=status]", "not_started")
    page.select_option("select[name=priority]", "medium")

    # Submit the form
    page.click("text=Save Task")

    # Should redirect to tasks list
    page.wait_for_url(f"{base_url}/tasks/")

    # Verify the task appears in the list
    assert page.locator("text=E2E Test Task").first.is_visible()

    # Verify via API that the task was created correctly
    resp = requests.get(f"{base_url}/api/tasks/", timeout=5)
    resp.raise_for_status()
    tasks = resp.json()["tasks"]
    task = [t for t in tasks if t["name"] == "E2E Test Task"][0]

    assert task["name"] == "E2E Test Task"
    assert task["description"] == "Test task without parent"
    assert task["project_id"] == project_id
    assert task["parent_task_id"] is None  # Should be None for no parent


@pytest.mark.playwright
def test_task_creation_with_parent(page, live_server):
    """Test creating a task with a parent task."""
    base_url = live_server["url"]

    # Create project and parent task via API
    project_data = {"name": "E2E Parent Test Project", "description": "Test project"}
    resp = requests.post(f"{base_url}/api/projects/", json=project_data, timeout=5)
    resp.raise_for_status()
    project_id = resp.json()["project"]["id"]

    parent_task_data = {
        "name": "Parent Task",
        "description": "Parent task",
        "project_id": project_id,
        "status": "in_progress",
        "priority": "high"
    }
    resp = requests.post(f"{base_url}/api/tasks/", json=parent_task_data, timeout=5)
    resp.raise_for_status()
    parent_task_id = resp.json()["task"]["id"]

    # Navigate to task creation page
    page.goto(f"{base_url}/tasks/new")

    # Fill out the task form with a parent task
    page.fill("input[name=name]", "Child Task")
    page.fill("textarea[name=description]", "Child task with parent")
    page.select_option("select[name=project_id]", str(project_id))

    # Wait for parent task options to load (they load dynamically based on project)
    page.wait_for_timeout(1000)
    page.select_option("select[name=parent_task_id]", str(parent_task_id))

    page.select_option("select[name=status]", "not_started")
    page.select_option("select[name=priority]", "low")

    # Submit the form
    page.click("text=Save Task")

    # Should redirect to tasks list
    page.wait_for_url(f"{base_url}/tasks/")

    # Verify the task appears in the list
    assert page.locator("text=Child Task").first.is_visible()

    # Verify via API that the task was created correctly
    resp = requests.get(f"{base_url}/api/tasks/", timeout=5)
    resp.raise_for_status()
    tasks = resp.json()["tasks"]
    task = [t for t in tasks if t["name"] == "Child Task"][0]

    assert task["name"] == "Child Task"
    assert task["parent_task_id"] == parent_task_id
    assert task["project_id"] == project_id


@pytest.mark.playwright
def test_project_creation_and_navigation(page, live_server):
    """Test creating a project and navigating through the interface."""
    base_url = live_server["url"]

    # Navigate to project creation page
    page.goto(f"{base_url}/projects/new")

    # Fill out the project form
    page.fill("input[name=name]", "E2E Navigation Project")
    page.fill("textarea[name=description]", "Project for testing navigation")

    # Submit the form
    page.click("text=Save Project")

    # Should redirect to projects list
    page.wait_for_url(f"{base_url}/projects/")

    # Verify the project appears in the list
    assert page.locator("text=E2E Navigation Project").first.is_visible()

    # Test navigation to different pages
    page.click("text=Tasks")
    page.wait_for_url(f"{base_url}/tasks/")

    page.click("text=Reminders")
    page.wait_for_url(f"{base_url}/reminders/")

    page.click("text=Projects")
    page.wait_for_url(f"{base_url}/projects/")

    # Verify project is still visible after navigation
    assert page.locator("text=E2E Navigation Project").first.is_visible()


@pytest.mark.playwright
def test_form_validation_errors(page, live_server):
    """Test form validation for required fields."""
    base_url = live_server["url"]

    # Test project form validation
    page.goto(f"{base_url}/projects/new")
    page.click("text=Save Project")  # Submit without filling required fields

    # Should stay on the same page and show validation errors
    assert page.url.endswith("/projects/new")
    # Check for validation error indicators (field highlighting, error messages, etc.)

    # Test task form validation
    page.goto(f"{base_url}/tasks/new")
    page.click("text=Save Task")  # Submit without filling required fields

    # Should stay on the same page
    assert page.url.endswith("/tasks/new")

    # Test reminder form validation
    page.goto(f"{base_url}/reminders/new")
    page.click("text=Save Reminder")  # Submit without filling required fields

    # Should stay on the same page
    assert page.url.endswith("/reminders/new")


@pytest.mark.playwright
def test_registration_validation_error(page, live_server):
    base_url = live_server["url"]
    page.goto(f"{base_url}/register/")
    page.fill("input[name=username]", "bad")
    page.fill("input[name=email]", "<EMAIL>")
    page.fill("input[name=password]", "secret123")
    page.fill("input[name=confirm]", "mismatch")
    page.click("text=Register")

    # Better assertion with error message context - check for flash messages
    # Flash messages are in divs with border-l-4 class and contain error text
    error_locator = page.locator(".border-l-4")
    assert error_locator.is_visible()
    error_text = error_locator.inner_text()
    assert "Passwords must match" in error_text, f"Expected 'Passwords must match' in error message, got: {error_text}"
