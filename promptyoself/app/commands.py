# -*- coding: utf-8 -*-
"""Click commands."""
import os
from glob import glob
from subprocess import call

import click
import datetime as dt  # Added dt import
from datetime import timedelta  # Specific import for timedelta
from .database import db
# Assuming User is already imported if needed elsewhere or can be added
from .models import Project, Task, Reminder

HERE = os.path.abspath(os.path.dirname(__file__))
PROJECT_ROOT = os.path.join(HERE, os.pardir)
TEST_PATH = os.path.join(PROJECT_ROOT, "tests")


@click.command()
@click.option(
    "-c/-C",
    "--coverage/--no-coverage",
    default=True,
    is_flag=True,
    help="Show coverage report",
)
@click.option(
    "-k",
    "--filter",
    default=None,
    help="Filter tests by keyword expressions",
)
def test(coverage, filter):
    """Run the tests."""
    import pytest

    args = [TEST_PATH, "--verbose"]
    if coverage:
        args.append("--cov=promptyoself.app")
    if filter:
        args.extend(["-k", filter])
    rv = pytest.main(args=args)
    exit(rv)


@click.command()
@click.option(
    "-f",
    "--fix-imports",
    default=True,
    is_flag=True,
    help="Fix imports using isort, before linting",
)
@click.option(
    "-c",
    "--check",
    default=False,
    is_flag=True,
    help="Don't make any changes to files, just confirm they are formatted correctly",
)
def lint(fix_imports, check):
    """Lint and check code style with black, flake8 and isort."""
    skip = ["node_modules", "requirements", "migrations"]
    root_files = glob("*.py")
    root_directories = [
        name for name in next(os.walk("."))[1] if not name.startswith(".")
    ]
    files_and_directories = [
        arg for arg in root_files + root_directories if arg not in skip
    ]

    def execute_tool(description, *args):
        """Execute a checking tool with its arguments."""
        command_line = list(args) + files_and_directories
        click.echo(f"{description}: {' '.join(command_line)}")
        rv = call(command_line)
        if rv != 0:
            exit(rv)

    isort_args = []
    black_args = []
    if check:
        isort_args.append("--check")
        black_args.append("--check")
    if fix_imports:
        execute_tool("Fixing import order", "isort", *isort_args)
    execute_tool("Formatting style", "black", *black_args)
    execute_tool("Checking code style", "flake8")


@click.command()
def seed():
    """Seed the database with initial data."""
    from . import create_app  # Import factory

    # Explicitly create app and push context for this command
    # This ensures settings from .env are loaded if not already by Flask CLI runner
    # and that db.session operates within this context.
    app = create_app()
    with app.app_context():
        click.echo("Seeding database within explicit app context...")
        click.echo(
            f"Using database URI: {app.config.get('SQLALCHEMY_DATABASE_URI')}")

        # All database operations must be within the app_context
        # Create a project
        project1 = Project.create(name="Personal Errands",
                                 description="Tasks for personal life.")
        click.echo(f"Added project: {project1.name}")

        # Create a task for the project
        task1 = Task.create(
            name="Buy groceries",
            description="Milk, Eggs, Bread, Cheese",
            project_id=project1.id,  # Use project_id instead of project object
        )
        click.echo(f"Added task: {task1.name} to project {project1.name}")

        # Create another task for the project
        task2 = Task.create(
            name="Schedule doctor appointment",
            description="Annual check-up",
            project_id=project1.id,  # Use project_id instead of project object
        )
        click.echo(f"Added task: {task2.name} to project {project1.name}")

        # Create a reminder for task1
        # Removed incorrect local import and comments from previous attempt
        Reminder.create(
            message="Don't forget the milk!",
            next_run=dt.datetime.now(dt.timezone.utc) +
            timedelta(hours=2),  # Use dt alias
            process_name="grocery_agent_v1",
            task_id=task1.id,  # Use task_id instead of task object
        )
        click.echo(f"Added reminder for task: {task1.name}")

        # Create a reminder for task2
        Reminder.create(
            message="Call Dr. Smith's office before 5 PM.",
            next_run=dt.datetime.now(dt.timezone.utc) +
            timedelta(days=1),  # Use dt alias
            process_name="appointment_scheduler_agent",
            task_id=task2.id  # Use task_id instead of task object
        )
        click.echo(f"Added reminder for task: {task2.name}")

        try:  # Correctly indented try
            db.session.commit()
            click.echo("Successfully seeded database.")
        except Exception as e:  # Correctly indented except
            db.session.rollback()
            click.echo(f"Error seeding database: {e}")
            click.echo(f"Exception type: {type(e)}")  # More detailed error
            exit(1)
