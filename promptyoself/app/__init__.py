# -*- coding: utf-8 -*-
"""The app module, containing the app factory function."""
import logging
import sys
import os
import datetime as dt

from flask import Flask, render_template
from typing import Any, Tuple, cast, Optional, Dict
# Removed unused imports, they are now in extensions.py

from . import commands
# Removed unused import
from .extensions import (
    bcrypt,
    cache,
    csrf_protect,
    db,
    debug_toolbar,
    flask_static_digest,
    limiter,
    login_manager,
    migrate,
    scheduler
)
# Added projects and tasks
from .api import reminders as api_reminders, projects as api_projects, tasks as api_tasks, internal as api_internal
from .ui import public, user, reminders, projects, tasks  # Added projects and tasks
from .models import User  # Import User model to resolve reference errors


def create_app(config_object: str = "promptyoself.app.settings") -> Flask:
    """Create application factory, as explained here: https://flask.palletsprojects.com/en/3.0.x/patterns/appfactories/.

    :param config_object: The configuration object to use.
    """
    template_dir = os.path.join(os.path.dirname(__file__), "..", "templates")
    static_dir = os.path.join(os.path.dirname(__file__), "..", "static")
    app = Flask(__name__.split(".")[
                0], template_folder=template_dir, static_folder=static_dir)
    app.config.from_object(config_object)

    # Configure scheduler to use memory job store to avoid pickling issues
    app.config['SCHEDULER_JOBSTORES'] = {
        'default': {'type': 'memory'}
    }

    # Force absolute path for SQLite to ensure consistency, overriding .env/.flaskenv if they are misbehaving
    # This is a debugging step; ideally, .env/.flaskenv loading should work as expected.
    if app.config.get("ENV", "") == "development" and app.config.get("SQLALCHEMY_DATABASE_URI", "").startswith("sqlite:///instance"):  # type: ignore
        db_path = "/workspace/promptyoself/instance/dev.sqlite3"
        # Ensure the directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        app.config["SQLALCHEMY_DATABASE_URI"] = f"sqlite:///{db_path}"
        app.logger.info(
            f"Overrode SQLALCHEMY_DATABASE_URI to absolute path: {app.config['SQLALCHEMY_DATABASE_URI']}")

    register_extensions(app)
    register_blueprints(app)
    register_errorhandlers(app)
    register_shellcontext(app)
    register_commands(app)
    # Register jobs first
    register_jobs(app)

    # Start the scheduler after all configurations and job registrations
    # Don't start scheduler during testing to avoid conflicts
    # type: ignore
    # type: ignore
    if not cast(bool, scheduler.running) and not cast(bool, app.config.get("TESTING", False)):  # type: ignore
        scheduler.start()

    @app.template_filter("date")
    def date_filter(value: Any, fmt: str = "%Y-%m-%d %H:%M:%S") -> str:  # type: ignore
        if value == "now":
            value = dt.datetime.now(dt.timezone.utc)
        if isinstance(value, (dt.date, dt.datetime)):
            return value.strftime(fmt)
        return value

    @app.context_processor
    def inject_current_year() -> Dict[str, int]:  # type: ignore
        return {"current_year": dt.datetime.now().year}

    configure_logger(app)
    return app


def register_extensions(app: Flask) -> None:
    """Register Flask extensions."""
    bcrypt.init_app(app)  # type: ignore
    cache.init_app(app)  # type: ignore
    db.init_app(app)  # type: ignore
    csrf_protect.init_app(app)  # type: ignore
    debug_toolbar.init_app(app)  # type: ignore
    migrate.init_app(app, db)  # type: ignore
    flask_static_digest.init_app(app)  # type: ignore
    limiter.init_app(app)  # type: ignore
    login_manager.init_app(app)  # type: ignore
    scheduler.init_app(app)  # type: ignore

    # Configure login manager
    login_manager.login_view = 'public.home'  # type: ignore
    login_manager.login_message = 'Please log in to access this page.'

    # User loader function
    @login_manager.user_loader  # type: ignore
    def load_user(user_id: str) -> Optional[User]:  # type: ignore
        return User.query.get(int(user_id))


def register_blueprints(app: Flask) -> None:
    """Register Flask blueprints."""
    # UI blueprints
    app.register_blueprint(public.blueprint)
    app.register_blueprint(user.blueprint)
    app.register_blueprint(reminders.blueprint)
    app.register_blueprint(projects.blueprint)  # Added projects blueprint
    app.register_blueprint(tasks.blueprint)  # Added tasks blueprint

    # API blueprints
    app.register_blueprint(api_reminders.blueprint)
    # Added projects API blueprint
    app.register_blueprint(api_projects.blueprint)
    app.register_blueprint(api_tasks.blueprint)  # Added tasks API blueprint
    # Added internal API blueprint
    app.register_blueprint(api_internal.blueprint)
    return None


def register_errorhandlers(app: Flask) -> None:
    """Register error handlers."""

    def render_error(error: Exception) -> Tuple[str, int]:
        """Render error template."""
        # If a HTTPException, pull the `code` attribute; default to 500
        error_code = getattr(error, "code", 500)
        error_message = getattr(error, "description", str(error))

        # Log the error with appropriate level
        if error_code == 404:
            app.logger.warning(
                f"404 Error: {error_message} - URL: {getattr(error, 'original_exception', 'N/A')}")
        elif error_code >= 500:
            app.logger.error(
                f"Server Error {error_code}: {error_message}", exc_info=True)
        else:
            app.logger.info(f"Client Error {error_code}: {error_message}")

        return render_template(f"{error_code}.html"), error_code

    def handle_exception(error: Exception) -> Tuple[str, int]:
        """Handle uncaught exceptions."""
        app.logger.error(f"Uncaught exception: {str(error)}", exc_info=True)
        return render_template("500.html"), 500

    # Register specific error handlers
    for errcode in [401, 403, 404, 500]:
        app.errorhandler(errcode)(render_error)  # type: ignore

    # Register generic exception handler for uncaught exceptions
    app.errorhandler(Exception)(handle_exception)  # type: ignore

    return None


def register_shellcontext(app: Flask) -> None:
    """Register shell context objects."""

    def shell_context() -> Dict[str, Any]:  # Changed object to Any
        """Shell context objects."""
        return {"db": db, "User": User}

    app.shell_context_processor(shell_context)  # type: ignore


def register_commands(app: Flask) -> None:
    """Register Click commands."""
    app.cli.add_command(commands.test)  # type: ignore
    app.cli.add_command(commands.lint)  # type: ignore
    app.cli.add_command(commands.seed)  # type: ignore


def register_jobs(app: Flask) -> None:
    """Register scheduled jobs."""
    # Jobs are now registered directly in the jobs module
    with app.app_context():  # type: ignore
        from .jobs import reminder_jobs
        reminder_jobs.register_jobs(app)  # type: ignore


def configure_logger(app: Flask) -> None:
    """Configure loggers."""
    # Don't configure logging if handlers already exist (prevents duplicate logs)
    if app.logger.handlers:  # type: ignore
        return

    # Set logging level based on environment
    if app.config.get('ENV') == 'development':  # type: ignore
        log_level = logging.DEBUG
    else:
        log_level = logging.INFO

    # Create formatter
    formatter = logging.Formatter(
        '[%(asctime)s] %(levelname)s in %(module)s: %(message)s'
    )

    # Console handler for all environments
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)

    # Add handlers to app logger
    app.logger.addHandler(console_handler)  # type: ignore
    app.logger.setLevel(log_level)  # type: ignore

    # Configure werkzeug logger to reduce noise in development
    if app.config.get('ENV') == 'development':  # type: ignore
        logging.getLogger('werkzeug').setLevel(logging.WARNING)

    # Log application startup
    app.logger.info(  # type: ignore
        # type: ignore
        # type: ignore
        # type: ignore
        # type: ignore
        f"Application starting in {app.config.get('ENV', 'unknown')} mode")
