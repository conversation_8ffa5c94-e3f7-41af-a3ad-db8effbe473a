import { test, expect } from '@playwright/test';

test('PromptYoSelf homepage loads correctly', async ({ page }) => {
  await page.goto('/');

  // Expect the page to have the correct title
  await expect(page).toHaveTitle(/PromptYoSelf/);

  // Check for main navigation elements
  await expect(page.getByText('PromptYoSelf')).toBeVisible();
});

test('navigation menu works', async ({ page }) => {
  await page.goto('/');

  // Check that main navigation links are present and clickable
  const homeLink = page.getByRole('link', { name: 'Home' });
  const projectsLink = page.getByRole('link', { name: 'Projects' });

  if (await homeLink.isVisible()) {
    await expect(homeLink).toBeVisible();
  }

  if (await projectsLink.isVisible()) {
    await expect(projectsLink).toBeVisible();
  }
});

test('application loads without errors', async ({ page }) => {
  // Listen for console errors
  const errors: string[] = [];
  page.on('console', msg => {
    if (msg.type() === 'error') {
      errors.push(msg.text());
    }
  });

  await page.goto('/');

  // Wait for page to fully load
  await page.waitForLoadState('networkidle');

  // Check that no critical errors occurred
  const criticalErrors = errors.filter(error =>
    !error.includes('favicon') &&
    !error.includes('404') &&
    !error.includes('warning')
  );

  expect(criticalErrors).toHaveLength(0);
});
