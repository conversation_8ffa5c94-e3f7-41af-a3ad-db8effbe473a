import { test, expect } from '@playwright/test';

test.describe('PromptYoSelf Application', () => {
  test.beforeEach(async ({ page }) => {
    // Go to the homepage before each test
    await page.goto('/');
  });

  test('homepage displays correctly', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/PromptYoSelf/);
    
    // Check for main heading or brand name
    const heading = page.locator('h1, .navbar-brand, .brand');
    await expect(heading.first()).toBeVisible();
  });

  test('no 500 errors on main pages', async ({ page }) => {
    const pages = ['/', '/projects', '/tasks', '/reminders'];
    
    for (const pagePath of pages) {
      const response = await page.goto(pagePath);
      
      // Check that we don't get a 500 error
      if (response) {
        expect(response.status()).not.toBe(500);
        
        // Also check that the page doesn't contain error messages
        const errorText = await page.textContent('body');
        expect(errorText).not.toContain('Internal Server Error');
        expect(errorText).not.toContain('500');
        expect(errorText).not.toContain('ModuleNotFoundError');
      }
    }
  });

  test('navigation works without errors', async ({ page }) => {
    // Test navigation links if they exist
    const navLinks = await page.locator('nav a, .navbar a').all();
    
    for (const link of navLinks.slice(0, 3)) { // Test first 3 links to avoid too many requests
      const href = await link.getAttribute('href');
      if (href && href.startsWith('/') && !href.includes('#')) {
        await link.click();
        
        // Wait for navigation to complete
        await page.waitForLoadState('networkidle');
        
        // Check that we didn't get an error page
        const bodyText = await page.textContent('body');
        expect(bodyText).not.toContain('Internal Server Error');
        expect(bodyText).not.toContain('500');
      }
    }
  });

  test('forms load without errors', async ({ page }) => {
    // Look for any forms on the page
    const forms = await page.locator('form').all();
    
    if (forms.length > 0) {
      // Check that forms are visible and don't have error states
      for (const form of forms) {
        await expect(form).toBeVisible();
        
        // Check for any error messages within forms
        const formText = await form.textContent();
        expect(formText).not.toContain('Error');
        expect(formText).not.toContain('Failed');
      }
    }
  });

  test('API endpoints respond correctly', async ({ page }) => {
    // Test that API endpoints don't return 500 errors
    const apiEndpoints = ['/api/health', '/api/projects', '/api/tasks'];
    
    for (const endpoint of apiEndpoints) {
      try {
        const response = await page.request.get(endpoint);
        
        // API should not return 500 errors
        expect(response.status()).not.toBe(500);
        
        // If it's a 404, that's okay - endpoint might not exist
        // If it's 401/403, that's okay - might need auth
        // But 500 indicates our import fixes didn't work
        if (response.status() === 500) {
          const responseText = await response.text();
          console.error(`500 error on ${endpoint}:`, responseText);
        }
      } catch (error) {
        // Network errors are okay for this test
        console.log(`Network error on ${endpoint}:`, error);
      }
    }
  });

  test('JavaScript loads without critical errors', async ({ page }) => {
    const jsErrors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        jsErrors.push(msg.text());
      }
    });

    page.on('pageerror', error => {
      jsErrors.push(error.message);
    });

    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Filter out non-critical errors
    const criticalErrors = jsErrors.filter(error => 
      !error.includes('favicon') && 
      !error.includes('404') &&
      !error.includes('net::ERR_') &&
      !error.toLowerCase().includes('warning')
    );

    if (criticalErrors.length > 0) {
      console.log('JavaScript errors found:', criticalErrors);
    }

    // We'll be lenient here - just log errors but don't fail the test
    // unless there are many critical errors
    expect(criticalErrors.length).toBeLessThan(5);
  });

  test('database operations work', async ({ page }) => {
    // Test that pages that require database access load correctly
    const dbPages = ['/', '/projects', '/tasks'];
    
    for (const pagePath of dbPages) {
      await page.goto(pagePath);
      
      // Check that we don't get database-related errors
      const pageContent = await page.textContent('body');
      expect(pageContent).not.toContain('database');
      expect(pageContent).not.toContain('SQLAlchemy');
      expect(pageContent).not.toContain('connection');
    }
  });
});
